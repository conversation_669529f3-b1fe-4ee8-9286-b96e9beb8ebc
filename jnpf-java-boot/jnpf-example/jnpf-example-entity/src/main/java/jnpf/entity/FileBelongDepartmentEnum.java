package jnpf.entity;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum FileBelongDepartmentEnum implements IEnum<String>{

    NUCLEAR_AND_SYSTEM_DEPARTMENT("核与系统工程事业部","-HYXT"),
    PROJECT_DEPARTMENT("项目部","-FZ"),
    MANAGEMENT_DEPARTMENT("经理部", "-GM"),
    INTEGRATED_MANAGEMENT_DEPARTMENT("综合管理部", "-ZH"),
    FINANCE_DEPARTMENT("财务部", "-CW"),
    COMMERCIAL_DEPARTMENT("商务部", "-SW"),
    ENGINEERING_DEPARTMENT("工程部", "-GC"),
    INTEGRATED_TEAM("综合队", "-ZD"),
    TECHNOLOGY_DEPARTMENT("技术部", "-JS"),
    SAFETY_SUPERVISION_DEPARTMENT("安全监督部", "-AQ"),
    SAFETY_ECO_FRIENDLY_DEPARTMENT("安全环保部", "-AQHBB"),
    SAFETY_QUALITY_CENTER("安全质量中心", "-AQZLZX"),
    QUALITY_ASSURANCE_DEPARTMENT("QA部", "-QA"),
    QUALITY_CONTROL_DEPARTMENT("QC部", "-QC"),
    MATERIALS_DEPARTMENT("物资部", "-WZ"),
    MACHINERY_AND_COMMUNICATION_TEAM("机通队", "-JT"),
    PIPE_WELDING_TEAM("管焊队", "-GH"),
    ELECTRICAL_INSTRUMENTATION_TEAM("-电仪队", "-DY"),
    PRECAST_FACTORY("预制加工厂", "-YZ"),
//    BOP_ENGINEERING_TEAM("BOP土建队", "-BT"),
    CONVENTIONAL_ISLAND_CIVIL_ENGINEERING_TEAM("土建队", "-CT");


    @JsonValue
    @EnumValue
    private final String desc;
    private final String value;

    FileBelongDepartmentEnum(String desc, String value) {
        this.desc = desc;
        this.value = value;
    }

    public static FileBelongDepartmentEnum valueOfDesc(String desc) {
        return ArrayUtil.firstMatch(i->i.getDesc().equals(desc), FileBelongDepartmentEnum.values());
    }

}
