package jnpf.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * 漳州-班组管理
 */
@FaModalName("班组管理")
@Data
@TableName("base_user")
public class TeamManageEntity implements Serializable {

    @TableId(value ="F_ID")
    private String id;

    @TableField("SOURCE_UNIT")
    private String sourceUnit;

    @TableField("F_REAL_NAME")
    private String realName;

    @TableField("F_ORGANIZE_ID")
    private String organizeId;

    @TableField("TEAM")
    private String team;

    @TableField("F_MOBILE_PHONE")
    private String mobilePhone;

    @TableField("IDENTIFICATION_NUMBER")
    private String identificationNumber;

}
