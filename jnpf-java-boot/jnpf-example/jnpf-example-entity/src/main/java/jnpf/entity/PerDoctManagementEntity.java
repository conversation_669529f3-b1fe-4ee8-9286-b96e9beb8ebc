package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 证件管理
 *
 * @版本： V3.5
 * @版权： xx有限公司（https://www.xx.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-03-28
 */
@Deprecated
@Data
@TableName("per_doct_management")
public class PerDoctManagementEntity {

    @TableId(value ="ID")
    private String id;

    @TableField(value = "NAME" , updateStrategy = FieldStrategy.DEFAULT)
    private String name;

    @TableField(value = "ID_CARD" , updateStrategy = FieldStrategy.DEFAULT)
    private String idCard;

    @TableField(value = "DOCT_NUMBER" , updateStrategy = FieldStrategy.DEFAULT)
    private String doctNumber;

    @TableField(value = "DOCT_NAME" , updateStrategy = FieldStrategy.DEFAULT)
    private String doctName;

    @TableField(value = "ISSUING_UNIT" , updateStrategy = FieldStrategy.DEFAULT)
    private String issuingUnit;

    @TableField(value = "JOB" , updateStrategy = FieldStrategy.DEFAULT)
    private String job;

    @TableField(value = "PROFESSIONAL_QUALIFICATIONS" , updateStrategy = FieldStrategy.DEFAULT)
    private String professionalQualifications;

    @TableField(value = "SKILL_LEVEL" , updateStrategy = FieldStrategy.DEFAULT)
    private String skillLevel;

    @TableField(value = "ISSUE_DATE" , updateStrategy = FieldStrategy.DEFAULT)
    private Date issueDate;

    @TableField(value = "REVIEW_DATE" , updateStrategy = FieldStrategy.DEFAULT)
    private Date reviewDate;

    @TableField(value = "EXPIRE_DATE" , updateStrategy = FieldStrategy.DEFAULT)
    private Date expireDate;

    @TableField(value = "APPROACH_DATE" , updateStrategy = FieldStrategy.DEFAULT)
    private Date approachDate;

    @TableField(value = "EXIT_DATE" , updateStrategy = FieldStrategy.DEFAULT)
    private Date exitDate;

    @TableField(value = "AUDIT_RESULTS" , updateStrategy = FieldStrategy.DEFAULT)
    private Integer auditResults;

    @TableField(value = "OCCUPATIONAL_HEALTH" , updateStrategy = FieldStrategy.DEFAULT)
    private String occupationalHealth;

    @TableField(value = "SOURCE_UNIT" , updateStrategy = FieldStrategy.DEFAULT)
    private String sourceUnit;

    @TableField(value = "DEPT" , updateStrategy = FieldStrategy.DEFAULT)
    private String dept;

    @TableField(value = "DRIVING_TYPE" , updateStrategy = FieldStrategy.DEFAULT)
    private String drivingType;

    @TableField(value = "SENIORITY" , updateStrategy = FieldStrategy.DEFAULT)
    private String seniority;

    @TableField("F_FLOW_ID")
    private String flowId;
}
