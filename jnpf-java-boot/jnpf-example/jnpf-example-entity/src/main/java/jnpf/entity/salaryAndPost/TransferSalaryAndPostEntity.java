package jnpf.entity.salaryAndPost;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.util.Date;

/**
 * 漳州-调薪调岗
 */
@FaModalName("调薪调岗")
@Data
@TableName("ext_transfer_salary_post")
public class TransferSalaryAndPostEntity extends SuperEntity<String> {

    @ExcelIgnore
    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("部门")
    private String dept;

    @ExcelIgnore
    @TableField(exist = false)
    private String deptName;

    @ExcelProperty("岗位")
    private String job;

    @ExcelIgnore
    @TableField(exist = false)
    private String jobName;

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("身份证号")
    private String idCard;

    @ExcelProperty("入场时间")
    private Date entranceTime;

    @ExcelProperty("入场天数")
    private String entranceDay;

    @ExcelProperty("年龄")
    private Integer age;

    @ExcelProperty("工作年限")
    private String workingYear;

    @ExcelProperty("考核分数")
    private Integer score;

    @ExcelProperty("定岗（元/天）")
    private String settlePost;

    @ExcelIgnore
    @TableField(exist = false)
    private String settlePostName;

    @ExcelProperty("备注")
    private String remark;

    private String fFlowId;

    private Integer currentState;


}
