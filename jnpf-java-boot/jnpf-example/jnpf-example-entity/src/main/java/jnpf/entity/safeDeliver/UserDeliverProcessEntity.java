package jnpf.entity.safeDeliver;

import jnpf.entity.test.BaseEduUserInfoEntity;
import jnpf.enums.studyspecialapproval.StudySpecialTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class UserDeliverProcessEntity extends BaseEduUserInfoEntity {

    private String deliverId;
    private String deliverRecordId;
    private String deliverName;
    private String deliverNum;
    private String deliverClassName;
    private StudySpecialTypeEnum deliverType;
    private String deliverTeacherId;

    private Date planStartTime;
    private Date planEndTime;

    private Boolean isEffective = true;

    private Integer totalWatchedTime;
    private Integer totalDuration;
    private String formatTotalWatchedTime;
    private String formatTotalDuration;
    //培训记录创建时间
    private Date deliverCreatorTime;
    private String deliverProcess;
    private Double deliverProcessNum;
    private Integer score;
    private Date testTime;

    //是否需要考试:0=不需要，1=需要
    private Integer isNeedTest;
    //考试时是否需要人脸识别:0=不需要，1=需要
    private Integer isNeedFaceCheck;
    //是否需要签到:0=不需要，1=需要
    private Integer isNeedSign;
    //是否需要签署承诺书:0=不需要，1=需要
    private Integer isNeedSignCommit;

    private Integer isNeedEvaluate;

    //是否及格:0=不及格，1=及格
    private Integer isPass;
    // 及格分
    private Integer passScore;
    private String isPassExcel;
    //是否参加考试(0:未参加 1:已参加)
    private Integer isTest;
    private Integer isTakePic;
    private Boolean isTakeVideo;

    private Long lastTestScore;

    private Integer lastTestPassStatus;

    //是否签到:0=未签到，1=已签到
    private Integer isSign;
    //是否签到:0=未签署，1=已签署
    private Integer isSignCommit;
    //是否完成文档学习
    private Boolean isFileStudyFinish;
    private Boolean isOnePageFinish;
    private Integer isEvaluated;

    private Integer totalExamCount;
    private Integer remainExamCount;

    private Boolean isFinish = false;

    private int docCount = 0; // 文档数量
    private int videoCount = 0; // 视频数量

}


