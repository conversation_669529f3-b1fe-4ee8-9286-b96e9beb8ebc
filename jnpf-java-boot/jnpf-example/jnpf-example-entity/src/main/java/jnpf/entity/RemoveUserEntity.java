package jnpf.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jnpf.base.entity.SuperEntity;
import lombok.Data;
import java.util.Date;
/**
 * 用户信息
 *
 * @版本： V3.5
 * @版权： xx有限公司（https://www.xx.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-06-12
 */
@Data
@TableName("base_user")
public class RemoveUserEntity extends SuperEntity<String> {

    @TableId(value ="F_ID"  )
    private String id;

    @TableField("F_ACCOUNT")
    private String account;

    @TableField(value = "F_REAL_NAME" , updateStrategy = FieldStrategy.DEFAULT)
    private String realName;

    @TableField(value = "SOURCE_UNIT" , updateStrategy = FieldStrategy.DEFAULT)
    private String sourceUnit;

    @TableField("F_QUICK_QUERY")
    private String quickQuery;

    @TableField("F_NICK_NAME")
    private String nickName;

    @TableField("F_HEAD_ICON")
    private String headIcon;

    @TableField(value = "F_GENDER" , updateStrategy = FieldStrategy.DEFAULT)
    private Integer gender;

    @TableField(value = "F_BIRTHDAY" , updateStrategy = FieldStrategy.DEFAULT)
    private Date birthday;

    @TableField(value = "F_MOBILE_PHONE" , updateStrategy = FieldStrategy.DEFAULT)
    private String mobilePhone;

    @TableField("F_TELE_PHONE")
    private String telePhone;

    @TableField(value = "IDENTIFICATION_NUMBER" , updateStrategy = FieldStrategy.DEFAULT)
    private String identificationNumber;

    @TableField(value = "TRAINING_TIME" , updateStrategy = FieldStrategy.DEFAULT)
    private Date trainingTime;

    @TableField("TRAINING_STATUS")
    private String trainingStatus;

    @TableField(value = "GO_PROJECT_TIME" , updateStrategy = FieldStrategy.DEFAULT)
    private Date goProjectTime;

    @TableField(value = "TEAM" , updateStrategy = FieldStrategy.DEFAULT)
    private String team;

    @TableField(value = "CATEGORY_PERSONNEL" , updateStrategy = FieldStrategy.DEFAULT)
    private String categoryPersonnel;

    @TableField(value = "NATURE_ACCOUNT" , updateStrategy = FieldStrategy.DEFAULT)
    private String natureAccount;

    @TableField(value = "FORM_EMPLOYMENT" , updateStrategy = FieldStrategy.DEFAULT)
    private String formEmployment;

    @TableField(value = "REMARK" , updateStrategy = FieldStrategy.DEFAULT)
    private String remark;

    @TableField("SENIORITY")
    private Integer seniority;

    @TableField("F_LANDLINE")
    private String landline;

    @TableField("F_EMAIL")
    private String email;

    @TableField(value = "F_NATION" , updateStrategy = FieldStrategy.DEFAULT)
    private String nation;

    @TableField(value = "F_NATIVE_PLACE" , updateStrategy = FieldStrategy.DEFAULT)
    private String nativePlace;

    @TableField("F_ENTRY_DATE")
    private Date entryDate;

    @TableField("F_CERTIFICATES_TYPE")
    private String certificatesType;

    @TableField("F_CERTIFICATES_NUMBER")
    private String certificatesNumber;

    @TableField(value = "F_EDUCATION" , updateStrategy = FieldStrategy.DEFAULT)
    private String education;

    @TableField("F_URGENT_CONTACTS")
    private String urgentContacts;

    @TableField("F_URGENT_TELE_PHONE")
    private String urgentTelePhone;

    @TableField("F_POSTAL_ADDRESS")
    private String postalAddress;

    @TableField("F_SIGNATURE")
    private String signature;

    @TableField("F_PASSWORD")
    private String password;

    @TableField("F_SECRETKEY")
    private String secretkey;

    @TableField("F_FIRST_LOG_TIME")
    private Date firstLogTime;

    @TableField("F_FIRST_LOG_IP")
    private String firstLogIp;

    @TableField("F_PREV_LOG_TIME")
    private Date prevLogTime;

    @TableField("F_PREV_LOG_IP")
    private String prevLogIp;

    @TableField("F_LAST_LOG_TIME")
    private Date lastLogTime;

    @TableField("F_LAST_LOG_IP")
    private String lastLogIp;

    @TableField("F_LOG_SUCCESS_COUNT")
    private Integer logSuccessCount;

    @TableField("F_LOG_ERROR_COUNT")
    private Integer logErrorCount;

    @TableField("F_CHANGE_PASSWORD_DATE")
    private Date changePasswordDate;

    @TableField("F_LANGUAGE")
    private String language;

    @TableField("F_THEME")
    private String theme;

    @TableField("F_COMMON_MENU")
    private String commonMenu;

    @TableField("F_IS_ADMINISTRATOR")
    private Integer isAdministrator;

    @TableField("F_PROPERTY_JSON")
    private String propertyJson;

    @TableField("F_MANAGER_ID")
    private String managerId;

    @TableField(value = "F_ORGANIZE_ID" , updateStrategy = FieldStrategy.DEFAULT)
    private String organizeId;

    @TableField(value = "F_POSITION_ID" , updateStrategy = FieldStrategy.DEFAULT)
    private String positionId;

    @TableField("F_ROLE_ID")
    private String roleId;

    @TableField("F_PORTAL_ID")
    private String portalId;

    @TableField("F_LOCK_MARK")
    private Integer lockMark;

    @TableField("F_UNLOCK_TIME")
    private Date unlockTime;

    @TableField("F_GROUP_ID")
    private String groupId;

    @TableField("F_SYSTEM_ID")
    private String systemId;

    @TableField("F_HANDOVER_MARK")
    private Integer handoverMark;

    @TableField("F_APP_SYSTEM_ID")
    private String appSystemId;

    @TableField("F_DING_JOB_NUMBER")
    private String dingJobNumber;

    @TableField("F_DESCRIPTION")
    private String description;

    @TableField("F_ENABLED_MARK")
    private Integer enabledMark;

    @TableField("F_SORT_CODE")
    private Long sortCode;

    @TableField("F_TENANT_ID")
    private String tenantId;

    @TableField("F_HANDOVER_USERID")
    private String handoverUserid;

    @TableField("TRAINING_REMARK")
    private String trainingRemark;

    @TableField("PRESENCE_STATUS")
    private Integer presenceStatus;

    @TableField("SECURITY_LEVEL1")
    private Integer securityLevel1;

    @TableField("SECURITY_LEVEL2")
    private Integer securityLevel2;

    @TableField("SECURITY_LEVEL3")
    private Integer securityLevel3;

    @TableField("QUALITY_TRAINING")
    private Integer qualityTraining;

    @TableField("CORPORATE_CULTURE")
    private Integer corporateCulture;

    @TableField("FIRST_LOGIN_STATUS")
    private Integer firstLoginStatus;

    @TableField("F_FLOW_ID")
    private String flowId;

    @TableField("DOCT_STATUS")
    private Integer doctStatus;

    @TableField(value = "AGE" , updateStrategy = FieldStrategy.DEFAULT)
    private Integer age;

    @TableField("CERTIFICATE_STATUS")
    private String certificateStatus;

    @TableField("GLOBAL_STATE")
    private Integer globalState;

    @TableField(value = "F_BLOOD_TYPE" , updateStrategy = FieldStrategy.DEFAULT)
    private String bloodType;

    @TableField(value = "REMOVE_TIME" , updateStrategy = FieldStrategy.DEFAULT)
    private Date removeTime;

    @TableField(value = "REMOVE_REMARK" , updateStrategy = FieldStrategy.DEFAULT)
    private String removeRemark;

    /** 岗位层级 */
    @TableField("POSITION_LEVEL")
    private String positionLevel;

    /** 岗位序列 */
    @TableField("POSITION_SEQUENCE")
    private String positionSequence;

    /** 项目 */
    @TableField("PROJECT")
    private String project;

    /** 政治面貌 */
    @TableField("POLITICAL_OUTLOOK")
    private String politicalOutlook;

    /** 毕业学校 */
    @TableField("GRADUATION_SCHOOL")
    private String graduationSchool;

    /** 所学专业 */
    @TableField("SPECIALTY")
    private String specialty;

    /** 毕业时间 */
    @TableField(value = "GRADUATION_TIME" , updateStrategy = FieldStrategy.DEFAULT)
    private Date graduationTime;

    /** 参加工作时间 */
    @TableField(value = "JOIN_WORK_TIME" , updateStrategy = FieldStrategy.DEFAULT)
    private Date joinWorkTime;

    /** 加入5公司时间 */
    @TableField(value = "JOIN_FIVE_FIRM_TIME" , updateStrategy = FieldStrategy.DEFAULT)
    private Date joinFiveFirmTime;

    /** 专业技术职称 */
    @TableField("TECHNICAL_TITLE")
    private String technicalTitle;

    /** 工人技能等级 */
    @TableField("WORKER_SKILL_LEVEL")
    private String workerSkillLevel;

    /** 职业资格 */
    @TableField("PROFESSIONAL_QUAL")
    private String professionalQual;

    /** 来源分类 */
    @TableField("SOURCE_CLASSIFY")
    private String sourceClassify;

    /** 核电建设经历 */
    @TableField("CONSTRUCTION_EXPERIENCE")
    private String constructionExperience;

    /** 办公地点 */
    @TableField("OFFICE_LOCATION")
    private String officeLocation;

    /** 办公电话 */
    @TableField("OFFICE_PHONE")
    private String officePhone;

    /** 紧急联系人 */
    @TableField("EMERGENCY_CONTACTS")
    private String emergencyContacts;

    /** 紧急联系人电话 */
    @TableField("EMERGENCY_CONTACTS_PHONE")
    private String emergencyContactsPhone;

    /** 婚姻状况 */
    @TableField("MARITAL_STATUS")
    private String maritalStatus;

    /** 人数 */
    @TableField("NUMBER")
    private String number;

    /** 家庭住址 */
    @TableField("HOME_ADDRESS")
    private String homeAddress;

    /** 一二期 */
    @TableField("PHASE")
    private String phase;

    /** 内部工龄 */
    @TableField("INTERIOR_SENIORITY")
    private Integer interiorSeniority;

    /**
     * {@link #getGlobalState()}用户全局状态
     */
    public static class GlobalState {
        /** 撤场？？？ */
        public static final Integer REMOVE = 3;
    }

    private int entryMark;

}
