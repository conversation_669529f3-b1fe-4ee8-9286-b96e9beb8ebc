package jnpf.entity.specialWork;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

/**
 * 特种作业类型
 */
@FaModalName("特种作业类型")
@Data
@TableName("ext_special_work_type")
public class SpecialWorkTypeEntity extends SuperEntity<String> {

    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("特种作业类型")
    private String specialWorkType;

}
