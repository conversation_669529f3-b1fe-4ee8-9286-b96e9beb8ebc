package jnpf.entity.machine;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.base.entity.SuperEntity;
import jnpf.base.entity.SuperFlowEntity;
import lombok.Data;

import java.util.Date;

/**
 * 漳州-计量器具台账——计量器具检定/校准确认记录申请单
 */
@FaModalName("计量器具台账——计量器具检定/校准确认记录申请单")
@Data
@TableName("ext_machine_verification_order")
public class MachineVerificationOrderEntity extends SuperFlowEntity<String> {

    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("编制人")
    private String compilationPer;

    @ExcelProperty("编制日期")
    private Date compilationTime;

    @ExcelProperty("流水单号")
    private String invoiceNumber;

    private Integer state;


}
