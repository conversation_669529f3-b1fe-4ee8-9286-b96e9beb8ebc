package jnpf.entity.settlePostWage;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

/**
 * 漳州-调薪表
 */
@FaModalName("调薪表")
@Data
@TableName("ext_settle_wage")
public class SettleWageEntity extends SuperEntity<String> {

    @ExcelIgnore
    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("姓名")
    @TableField(exist = false)
    private String userName;

//    @ExcelProperty("姓名")
    @ExcelIgnore
    private String name;

    @ExcelProperty("部门")
    @TableField(exist = false)
    private String userDept;

//    @ExcelProperty("部门")
    @ExcelIgnore
    private String dept;

    @ExcelProperty("薪资")
    private String wage;

    @ExcelProperty("是否旧薪资")
    @TableField(exist = false)
    private String userOldWage;

    /**
     * 1:新数据，2：旧数据
     */
//    @ExcelProperty("是否旧薪资")
    @ExcelIgnore
    private Integer oldWage;

}
