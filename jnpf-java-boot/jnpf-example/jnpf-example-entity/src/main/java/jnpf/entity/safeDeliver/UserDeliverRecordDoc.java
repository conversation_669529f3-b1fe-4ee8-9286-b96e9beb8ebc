package jnpf.entity.safeDeliver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.SqlEquals;
import jnpf.base.entity.SuperEntity;
import lombok.Data;


/**
 * 用户参与交底记录视频播放记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-18 15:31:39
 */
@Data
@TableName("ext_user_deliver_record_doc")
public class UserDeliverRecordDoc extends SuperEntity<String> {

    @TableId(type = IdType.AUTO)
    private String id;

    // 交底计划id
    @SqlEquals
    private String deliverId;

    private String deliverNum;

    // 用户交底记录ID
    @SqlEquals
    private String deliverRecordId;

    @SqlEquals
    private String userId;

    @SqlEquals
    private String docFileId;

    private String docName;

    @SqlEquals
    private Integer watchStatus = 0;

    private Integer watchedTime = 0;

    private Integer initialTime = 0;

    private Integer duration;

    @TableField(exist = false)
    private Long size;

    @TableField(exist = false)
    private String fullUrl;

    //交底档案附件,0: 不是, 1是
    @SqlEquals
    private String deliverStatus;

}
