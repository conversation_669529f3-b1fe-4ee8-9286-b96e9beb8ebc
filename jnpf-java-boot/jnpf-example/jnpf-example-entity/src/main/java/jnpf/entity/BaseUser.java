package jnpf.entity;


import jnpf.annotation.FaModalName;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperExtendEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright xx有限公司
 * @date 2019年9月26日 上午9:18
 */
@FaModalName("用户表")
@Data
@TableName("base_user")
public class BaseUser extends SuperExtendEntity.SuperExtendDEEntity<String> {
    /**
     * 智慧工地用户id
     */
    @TableField("PERSONNEL_ID")
    private String personnelId;


    /**
     * 账户
     */
    @TableField("F_ACCOUNT")
    @SqlSearch
    private String account;

    /**
     * 姓名
     */
    @TableField("F_REAL_NAME")
    @SqlSearch
    private String realName;

    /**
     * 快速查询
     */
    @TableField("F_QUICK_QUERY")
    private String quickQuery;

    /**
     * 呢称
     */
    @TableField("F_NICK_NAME")
    @SqlSearch
    private String nickName;

    /**
     * 头像
     */
    @TableField("F_HEAD_ICON")
    private String headIcon;

    /**
     * 性别
     */
    @TableField("F_GENDER")
    @SqlSearch
    private Integer gender;

    /**
     * 生日
     */
    @TableField("F_BIRTHDAY")
    @SqlSearch
    private Date birthday;

    /**
     * 手机
     */
    @TableField("F_MOBILE_PHONE")
    @SqlSearch
    private String mobilePhone;

    /**
     * 电话
     */
    @TableField("F_TELE_PHONE")
    @SqlSearch
    private String telePhone;

    /**
     * F_Landline
     */
    @TableField("F_LANDLINE")
    private String landline;

    /**
     * 邮箱
     */
    @TableField("F_EMAIL")
    @SqlSearch
    private String email;

    /**
     * 民族
     */
    @TableField("F_NATION")
    @SqlSearch
    private String nation;

    /**
     * 籍贯
     */
    @TableField("F_NATIVE_PLACE")
    @SqlSearch
    private String nativePlace;

    /**
     * 入职日期
     */
    @TableField(value = "F_ENTRY_DATE", fill = FieldFill.UPDATE)
    private Date entryDate;

    /**
     * 证件类型
     */
    @TableField("F_CERTIFICATES_TYPE")
    private String certificatesType;

    /**
     * 证件号码
     */
    @TableField("F_CERTIFICATES_NUMBER")
    @SqlSearch
    private String certificatesNumber;

    /**
     * 文化程度
     */
    @TableField("F_EDUCATION")
    @SqlSearch
    private String education;

    /**
     * F_UrgentContacts
     */
    @TableField("F_URGENT_CONTACTS")
    private String urgentContacts;

    /**
     * 紧急电话
     */
    @TableField("F_URGENT_TELE_PHONE")
    private String urgentTelePhone;

    /**
     * 通讯地址
     */
    @TableField("F_POSTAL_ADDRESS")
    @SqlSearch
    private String postalAddress;

    /**
     * 自我介绍
     */
    @TableField("F_SIGNATURE")
    private String signature;

    /**
     * 密码
     */
    @TableField("F_PASSWORD")
    private String password;

    /**
     * 秘钥
     */
    @TableField("F_SECRETKEY")
    private String secretkey;

    /**
     * 首次登录时间
     */
    @TableField("F_FIRST_LOG_TIME")
    private Date firstLogTime;

    /**
     * 首次登录IP
     */
    @TableField("F_FIRST_LOG_IP")
    private String firstLogIp;

    /**
     * 前次登录时间
     */
    @TableField("F_PREV_LOG_TIME")
    private Date prevLogTime;

    /**
     * 前次登录IP
     */
    @TableField("F_PREV_LOG_IP")
    private String prevLogIp;

    /**
     * 最后登录时间
     */
    @TableField("F_LAST_LOG_TIME")
    private Date lastLogTime;

    /**
     * 最后登录IP
     */
    @TableField("F_LAST_LOG_IP")
    private String lastLogIp;

    /**
     * 登录成功次数
     */
    @TableField("F_LOG_SUCCESS_COUNT")
    private Integer logSuccessCount;

    /**
     * 登录错误次数
     */
    @TableField("F_LOG_ERROR_COUNT")
    private Integer logErrorCount;

    /**
     * 最后修改密码时间
     */
    @TableField("F_CHANGE_PASSWORD_DATE")
    private Date changePasswordDate;

    /**
     * 系统语言
     */
    @TableField("F_LANGUAGE")
    private String language;

    /**
     * 系统样式
     */
    @TableField("F_THEME")
    private String theme;

    /**
     * 常用菜单
     */
    @TableField("F_COMMON_MENU")
    private String commonMenu;

    /**
     * 是否管理员
     */
    @TableField("F_IS_ADMINISTRATOR")
    private Integer isAdministrator;

    /**
     * 扩展属性
     */
    @TableField("F_PROPERTY_JSON")
    private String propertyJson;

    /**
     * 主管主键
     */
    @TableField("F_MANAGER_ID")
    private String managerId;

    /**
     * 组织主键
     */
    @TableField("F_ORGANIZE_ID")
    private String organizeId;

    @TableField(exist = false)
    private String organizeName;

    @TableField(exist = false)
    private String organizeFullName;

    /**
     * 岗位主键
     */
    @TableField("F_POSITION_ID")
    private String positionId;

    @TableField(exist = false)
    private String positionName;

    /**
     * 角色主键
     */
    @TableField("F_ROLE_ID")
    private String roleId;

    @TableField(exist = false)
    private String roleName;


    /**
     * 门户主键
     */
    @TableField("F_PORTAL_ID")
    private String portalId;

    /**
     * 是否锁定
     */
    @TableField("F_LOCK_MARK")
    private Integer lockMark;

    /**
     * 解锁时间
     */
    @TableField(value = "F_UNLOCK_TIME", updateStrategy = FieldStrategy.DEFAULT)
    private Date unlockTime;

    /**
     * 分组id
     */
    @TableField("F_GROUP_ID")
    private String groupId;

    /**
     * 系统id
     */
    @TableField("F_SYSTEM_ID")
    private String systemId;

    /**
     * App系统id
     */
    @TableField("F_APP_SYSTEM_ID")
    private String appSystemId;

    /**
     * 钉钉工号
     */
    @TableField("F_DING_JOB_NUMBER")
    private String dingJobNumber;

    /**
     * 交接状态
     */
    @TableField("f_handover_mark")
    private Integer handoverMark;

    /**
     * 来源单位
     */
    @TableField("SOURCE_UNIT")
    private String sourceUnit;

    /**
     * 身份证
     */
    @TableField("IDENTIFICATION_NUMBER")
    @SqlSearch
    private String identificationNumber;

    /**
     * 培训时间
     */
    @TableField("TRAINING_TIME")
    private Date trainingTime;

    /**
     * 班组
     */
    @TableField("TEAM")
    @SqlSearch
    private String team;

    /**
     * 进入项目时间
     */
    @TableField("GO_PROJECT_TIME")
    private Date goProjectTime;

    /**
     * 人员类别
     */
    @TableField("CATEGORY_PERSONNEL")
    private String categoryPersonnel;

    /**
     * 户口性质
     */
    @TableField("NATURE_ACCOUNT")
    private String natureAccount;

    /**
     * 用工形式
     */
    @TableField("FORM_EMPLOYMENT")
    @SqlSearch
    private String formEmployment;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 工龄
     */
    @TableField("SENIORITY")
    private Integer seniority;

    /**
     * 培训备注
     */
    @TableField("TRAINING_REMARK")
    private String trainingRemark;

//    /**
//     * 资料状态
//     */
//    @TableField("PROFILE_STATUS")
//    private Integer profileStatus;

    /**
     * 在场状态
     */
    @TableField("PRESENCE_STATUS")
    private Integer presenceStatus;

//    /**
//     * 培训状态
//     */
//    @TableField("TRAINING_STATUS")
//    private Integer trainingStatus;

    /**
     * 安全一级
     */
    @TableField("SECURITY_LEVEL1")
    private Integer securityLevel1;

    /**
     * 安全二级
     */
    @TableField("SECURITY_LEVEL2")
    private Integer securityLevel2;

    /**
     * 安全三级
     */
    @TableField("SECURITY_LEVEL3")
    private Integer securityLevel3;

    /**
     * 质量培训
     */
    @TableField("QUALITY_TRAINING")
    private Integer qualityTraining;

    /**
     * 企业文化
     */
    @TableField("CORPORATE_CULTURE")
    private Integer corporateCulture;

    /**
     * 首次登入状态：1-首次登录，尚未修改初始密码/2-已经修改初始密码/3-已经修改初始资料
     */
    @TableField("FIRST_LOGIN_STATUS")
    private Integer firstLoginStatus;

    /**
     * 办证状态
     */
    @TableField("DOCT_STATUS")
    private Integer doctStatus;

    /**
     * 年龄
     */
    @TableField("AGE")
    private Integer age;


    /**
     * 持证情况
     */
    @TableField("CERTIFICATE_STATUS")
    private String certificateStatus;

    /**
     * 退场状态
     */
    @TableField("GLOBAL_STATE")
    private Integer globalState;

    /**
     * 血型
     */
    @TableField("F_BLOOD_TYPE")
    private String bloodType;

    /**
     * 退场时间
     */
    @TableField("REMOVE_TIME")
    private Date removeTime;

    /**
     * 退场原因
     */
    @TableField("REMOVE_REMARK")
    private Date removeRemark;


    // 信息来源
    @TableField(value = "from_source")
    private Integer fromSource;

    // 下发结果
    @TableField(value = "edu_result")
    private String eduResult;

    // 三级培训状态
    @TableField(value = "in_edu_status")
    private Integer inEduStatus;
}
