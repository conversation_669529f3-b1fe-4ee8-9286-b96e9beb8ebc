package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jnpf.base.entity.SuperBaseEntity;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.util.Date;

/**
 * 用户证件表（单附件模式）
 *
 * <AUTHOR>
 */
@Data
@TableName("base_user_document")
public class BaseUserDocument extends  SuperBaseEntity.SuperTBaseEntity<String> {

    /**
     * 证件唯一标识
     */
    @TableId(value = "f_id")
    private String id;

    /**
     * 关联用户ID (base_user.f_id)
     */
    @TableField("user_id")
    private String userId;

    /**
     * 证件类型
     */
    @TableField("document_type")
    private String documentType;

    /**
     * 证件附件ID（关联文件系统）
     */
    @TableField("attachment_id")
    private String attachmentId;

    /**
     * 有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("valid_start")
    private Date validStart;

    /**
     * 有效期截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("valid_end")
    private Date validEnd;

    /**
     * 状态（1-有效 0-过期 2-作废）
     */
    @TableField("cert_status")
    private Integer certStatus;

    /**
     * 排序号（同类证件排序）
     */
    @TableField("f_sort")
    private Integer sort;


}
