package jnpf.entity.blackList;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.util.Date;

/**
 * 黑名单台账
 */
@FaModalName("黑名单台账")
@Data
@TableName("ext_black_list")
public class BlackListEntity extends SuperEntity<String> {

    @ExcelIgnore
    @TableId(type = IdType.ASSIGN_UUID)
//    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("姓名")
    @SqlSearch
    private String name;

    @ExcelProperty("身份证")
    @SqlSearch
    private String idCard;

    @ExcelProperty("手机号")
    @SqlSearch
    private String phone;

    @ExcelProperty("年龄")
    private String age;

    @ExcelIgnore
    private Integer gender;

    @ExcelProperty("性别")
    @TableField(exist = false)
    private String genderName;

    //拉入黑名单原因
    @ExcelProperty("拉黑原因")
    private String addReason;

    // 拉黑时间
    @ExcelIgnore
    private Date addTime;

    @ExcelProperty("拉黑时间")
    @TableField(exist = false)
    private String addTimeStr;

    // 导入excel进行预览时，记录格式等异常信息，用于告知用户该条数据异常点
    @TableField(exist = false)
    private String errorMsg;
}
