package jnpf.entity.safeDeliver;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.SqlEquals;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 安全技术交底卡
 *
 * @版本： V3.5
 * @版权： xx有限公司（https://www.xx.com）
 * @作者： JNPF开发平台组
 * @日期： 2025-03-17
 */
@Data
@TableName("ext_deliver_card")
public class DeliverCardEntity extends SuperEntity<String> {
//
//    @TableField("f_id")
//    private String id;

    /* 子项(分类) */
    @SqlEquals
    private String kidItem;

    @SqlSearch
    private String kidItemName;

    /* 交底卡名称 */
    @SqlSearch
    private String cardName;

    /* 编号 */
    @SqlSearch
    private String num;

    /* 版本 */
    @SqlEquals
    private String version;

    /* 编制人 */
    @SqlEquals
    private String compilePer;

    @TableField(exist = false)
    private String compilePerName;

    /*是否最新版 */
    private Integer isNewst;

    /* 发布时间 */
    private Date releaseTime;

    /* 附件 */
    private Object appendFile;
//    private String appendFile;

    /* 专业 */
    @SqlSearch
    private String subject;

    /* 最新版 */
    private String newstVersion;

    /* 施工方案名称 */
    @SqlSearch
    private String constructName;

    /* 施工方案编号 */
    @SqlSearch
    private String constructNum;

    /* 施工方案版本 */
    @SqlSearch
    private String constructVersion;

    /*施工方案级别  */
    @SqlSearch
    private String constructLevel;

    @SqlEquals
    @TableField("F_FLOW_ID")
    private String flowId;

    /** 版本数量 */
    @TableField(exist = false)
    private Integer versionCount;

    /** 交底记录数量 */
    @TableField(exist = false)
    private Integer flowCount;

}
