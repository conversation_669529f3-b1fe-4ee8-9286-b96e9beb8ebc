package jnpf.entity.safeDeliver;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jnpf.annotation.SqlEquals;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperFlowEntity;
import jnpf.enums.studyspecialapproval.StudySpecialTypeEnum;
import jnpf.util.CollectionUtils;
import jnpf.util.StringUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 交底档案表
 *
 * @版本： V3.5
 * @版权： xx有限公司（https://www.xx.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-03-29
 */
@Data
@TableName("ext_deliver_record")
public class DeliverRecordEntity extends SuperFlowEntity<String> {

    @TableField(exist = false)
    private Boolean isFormalCreate;

    /* 档案名称 */
    @SqlSearch
    private String name;

    /* 档案编号 */
    @SqlSearch
    private String num;

    /* 交底分类*/
    @SqlEquals
    private String deliverClass;

    /* 交底分类名称*/
    @SqlSearch
    private String deliverClassName;

    /* 交底分类名称*/
    @SqlEquals
    private StudySpecialTypeEnum type;

    /* 部门 */
    @SqlEquals
    private String department;

//    @TableField(exist = false)
//    private String departmentList;

    @TableField(exist = false)
    private List<String> departmentNames;

    @Schema(description = "部门IDs")
    @TableField(exist = false)
    private List<Object> departmentIds;

    /* 地点*/
    private String location;

//    /** 是否需要考试(0:不考试 1:考试) */
//    @TableField(value = "F_IS_NEED_TEST")
//    private Integer isNeedTest;

    /**
     * 是否需要考试时人脸识别(0:不识别 1:识别)
     */
    private Integer isNeedFaceCheck;

    /**
     * 是否需要签到(0:不需要 1:需要)
     */
    private Integer isNeedSign;

    /**
     * 是否需要签署承诺书(0:不需, 1:需要)
     */
    private Integer isNeedSignCommit;

    /**
     * 学员是否评估(0:不需, 1:需要)
     */
    private Integer isNeedEvaluate;

    /**
     * 是否首次交底(0:不需, 1:需要)
     */
    private Integer isFirstDeliver;

    /* 重新交底情况 */
    private Integer reDeliverSituation;

//    /** 是否已安排(0:未安排 1:已安排) */
//    @TableField(value = "F_IS_USED")
//    private Integer isUsed;

    //fixme:为null时赋值为今日，待修复
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date planStartTime;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date planEndTime;

    private Object studyDuration;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer recognizeFrequency;

    @SqlEquals
    private String deliverCard;

    // 课件列表
    @TableField(exist = false)
    private List<DeliverCardEntity> deliverCardList;

    /* 人员id数组转字符串 */
    private String joiners;

//    @ExcelProperty("培训人员")
    @TableField(exist = false)
    private List<String> joinerNames;

    @Schema(description = "参与人员IDs")
    @TableField(exist = false)
    private List<String> joinerIds;

    @ExcelProperty("培训人数")
    @TableField(exist = false)
    private Integer joinerCount;

    /* 承诺*/
    private String commitment;

    /*注意事项*/
    private String additionalItem;

    /*答疑*/
    private String questions;

    /*班长签字时间*/
    private Date classLeaderSignTime;

    /* 附件 */
    private Object appendFile;

    private String version;

    /*施工部位*/
    private String constructSite;

    /*施工方案名称*/
    private String constructPlanName;

    /*施工方案编号*/
    private String constructPlanNum;

    /*交底对象*/
    private String deliverGroup;

    /*安全重要主控项目*/
    private String safeProjectContent;

    /*技术质量主控项目*/
    private String techProjectContent;

    @TableField(exist = false)
    private List<HashMap> safeProjectContentList;

    @TableField(exist = false)
    private List<HashMap> techProjectContentList;

    @TableField(exist = false)
    private List<String> picIds;

    @TableField(exist = false)
    private List<String> picUrls;

    @TableField("F_FLOW_TASK_ID")
    private String flowTaskId;

    @TableField("F_FLOW_ID")
    private String flowId;

    /* 获取交底卡IDs */
//    public List<String> getDeliverCardIds() {
//        return JSONObject.parseObject((String) this.getDeliverCard(), ArrayList.class);
//    }

    public void setSafeProjectInfoList(){
        if (StringUtil.isEmpty(this.safeProjectContent)) return;
        JSONArray contentList = JSONUtil.parseArray(getSafeProjectContent());
        List<HashMap> safeProjectContentList = new ArrayList<>();
        for (int i = 0; i < contentList.size(); i++) {
            HashMap<String, String> contentMap = new HashMap<>();
            JSONObject content = contentList.getJSONObject(i);
            contentMap.put("controllProj",content.getStr("controllProj"));
            contentMap.put("controllMethod",content.getStr("controllMethod"));
            safeProjectContentList.add(contentMap);
        }
        this.setSafeProjectContentList(safeProjectContentList);
    }

    public void setSafeProjectContent(){
        if (CollectionUtils.isEmpty(this.safeProjectContentList)) return;

        JSONArray jsonArray = new JSONArray();
        for (HashMap contentMap : this.safeProjectContentList) {
            com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
            jsonObject.put("controllProj",contentMap.get("controllProj").toString());
            jsonObject.put("controllMethod",contentMap.get("controllMethod").toString());
            jsonArray.add(jsonObject);
        }
        this.setSafeProjectContent(jsonArray.toString());
    }

    public void setTechProjectInfoList(){
        if (StringUtil.isEmpty(this.techProjectContent)) return;
        JSONArray contentList = JSONUtil.parseArray(getTechProjectContent());
        List<HashMap> techProjectContentList = new ArrayList<>();
        for (int i = 0; i < contentList.size(); i++) {
            HashMap<String, String> contentMap = new HashMap<>();
            JSONObject content = contentList.getJSONObject(i);
            contentMap.put("constructSite",content.getStr("constructSite"));
            contentMap.put("constructMethod",content.getStr("constructMethod"));
            contentMap.put("corePoint",content.getStr("corePoint"));
            techProjectContentList.add(contentMap);
        }
        this.setTechProjectContentList(techProjectContentList);
    }

    public void setTechProjectContent(){
        if (CollectionUtils.isEmpty(this.techProjectContentList)) return;

        JSONArray jsonArray = new JSONArray();
        for (HashMap contentMap : this.techProjectContentList) {
            com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
            jsonObject.put("constructSite",contentMap.get("constructSite").toString());
            jsonObject.put("constructMethod",contentMap.get("constructMethod").toString());
            jsonObject.put("corePoint",contentMap.get("corePoint").toString());
            jsonArray.add(jsonObject);
        }
        this.setTechProjectContent(jsonArray.toString());
    }




}
