package jnpf.entity.certificateManage;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

/**
 * 漳州-证件管理
 */
@FaModalName("证件管理-类型")
@Data
@TableName("certificate_manage_type")
public class CertificateManageTypeEntity extends SuperEntity<String> {

    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @SqlSearch
    @ExcelProperty("证件资质名称")
    private String name;

}
