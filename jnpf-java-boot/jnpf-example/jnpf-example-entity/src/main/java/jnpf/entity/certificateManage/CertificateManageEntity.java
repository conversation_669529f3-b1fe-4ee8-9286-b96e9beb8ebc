package jnpf.entity.certificateManage;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.annotation.SqlEquals;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.util.Date;

/**
 * 漳州-证件管理
 */
@FaModalName("证件管理")
@Data
@TableName("certificate_manage")
public class CertificateManageEntity extends SuperEntity<String> {

    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @SqlEquals
    @ExcelProperty("用户ID")
    private String userId;

    @SqlSearch
    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("身份证号")
    private String idCard;

    @ExcelProperty("手机号")
    private String phone;

    @ExcelProperty("证件照片")
    @TableField(exist = false)
    private String officialPhotoFileId;

    @SqlEquals
    @ExcelProperty("部门")
    private String dept;

    @ExcelProperty("部门名称")
    @TableField(exist = false)
    private String deptName;

    @SqlEquals
    @ExcelProperty("班组")
    private String team;

    @ExcelProperty("班组名称")
    @TableField(exist = false)
    private String teamName;

    @ExcelProperty("是否为监护人")
    private String isCustody;

    @SqlSearch
    @ExcelProperty("监护人编号")
    private String custodyNumber;

    @ExcelProperty("监护人自增序号")
    private Integer custodyNum;

    @ExcelProperty("以往参加培训情况")
    private String attendCondition;

    @ExcelProperty("有效期开始")
    private Date validStart;

    @ExcelProperty("有效期结束")
    private Date validEnd;

}
