package jnpf.entity.safeDeliver;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.SqlEquals;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperEntity;
import jnpf.enums.studyspecialapproval.StudySpecialTypeEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户参与交底记录表
 *
 * @版本： V3.5
 * @版权： xx有限公司（https://www.xx.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-04-12
 */
@Data
@TableName("ext_user_deliver_record")
public class UserDeliverRecordEntity extends SuperEntity<String>  {
    // 交底记录ID
    @SqlEquals
    private String deliverId;

    @SqlSearch
    private String deliverNum;

    @SqlEquals
    private String deliverClass;

    @SqlEquals
    @ExcelProperty("交底类型")
    private StudySpecialTypeEnum deliverType;

    @SqlEquals
    private String userId;

    @SqlSearch
    private String userName;

    /** 是否已签到(0:0:未签 1:已签) */
    //todo 改为Boolean类型
    @SqlEquals
    private Boolean isSign;

    /** 承诺书是否已签署(0:未签 1:已签) */
    @SqlEquals
    private Boolean isSignCommit;

    /** 是否已评价(0:未评价, 1:已评价) */
    @SqlEquals
    private Boolean isEvaluated;

    @SqlEquals
    private Boolean isTakePic;

    private String signImgBase64;

    private Date signTime;

    private String signCommitImgBase64;

    private Integer totalWatchedTime;

    private Integer totalDuration;

    @SqlEquals
    private Boolean isDelivered;

    @SqlEquals
    private Boolean isFileStudyFinish;

    @SqlEquals
    private Boolean isOnePageFinish;

    private String picId;

    @TableField(exist = false)
    private List<String> picList;

    /**
     * 当前状态
     */
    private Integer status;

}
