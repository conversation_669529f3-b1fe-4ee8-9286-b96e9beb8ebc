package jnpf.entity.certificateManage;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 漳州-证件导入管理
 */
@FaModalName("证件导入管理")
@Data
public class CertificateImportVO implements Serializable {

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("部门")
    private String dept;

    @ExcelProperty("培训名称")
    private String trainingName;

    @ExcelProperty("培训时间")
    private Date trainingTime;

    @ExcelProperty("考试成绩")
    private String trainingGrade;

    @ExcelProperty("是否合格")
    private String trainingQualified;

    @ExcelProperty("身份证号")
    private String idCard;

    @ExcelProperty("手机号")
    private String phone;

}
