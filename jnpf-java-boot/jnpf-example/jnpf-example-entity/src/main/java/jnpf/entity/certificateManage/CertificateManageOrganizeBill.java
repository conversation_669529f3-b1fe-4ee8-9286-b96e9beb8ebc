package jnpf.entity.certificateManage;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.annotation.SqlEquals;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.util.Date;

/**
 * 漳州-证件班组序号
 */
@FaModalName("证件管理")
@Data
@TableName("certificate_manage_organize_bill")
public class CertificateManageOrganizeBill extends SuperEntity<String> {

    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @SqlEquals
    @ExcelProperty("部门编码")
    private String deptNo;

    @ExcelProperty("当前序号")
    private Integer num;

}
