package jnpf.entity.settlePostWage;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import jnpf.annotation.FaModalName;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.util.Date;

/**
 * 漳州-转岗/转部门明细表
 */
@FaModalName("转岗/转部门明细表")
@Data
@TableName("ext_change_org")
public class ChangeOrgEntity extends SuperEntity<String> {

    @ExcelIgnore
    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("来源单位")
    @JSONField(name = "sourceUnit")
    @Excel(name = "来源单位",orderNum = "1", isImportField = "true" )
    private String sourceUnit;

    @ExcelProperty("用工形式")
    @JSONField(name = "employmentForm")
    @Excel(name = "用工形式",orderNum = "1", isImportField = "true" )
    private String employmentForm;

    @ExcelProperty("部门")
    @JSONField(name = "dept")
    @Excel(name = "部门",orderNum = "1", isImportField = "true" )
    private String dept;

    @ExcelProperty("岗位")
    @JSONField(name = "job")
    @Excel(name = "岗位",orderNum = "1", isImportField = "true" )
    private String job;

    @ExcelProperty("姓名")
    @JSONField(name = "name")
    @Excel(name = "姓名",orderNum = "1", isImportField = "true" )
    private String name;

    @ExcelProperty("身份证号")
    @JSONField(name = "idCard")
    @Excel(name = "身份证号",orderNum = "1", isImportField = "true" )
    private String idCard;

    @ExcelProperty("转岗时间")
    @JSONField(name = "changeTime")
    @Excel(name = "转岗时间",orderNum = "1", isImportField = "true" )
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date changeTime;

    @ExcelProperty("转岗后来源单位")
    @JSONField(name = "changeSourceUnit")
    @Excel(name = "转岗后来源单位",orderNum = "1", isImportField = "true" )
    private String changeSourceUnit;

    @ExcelProperty("转岗后用工形式")
    @JSONField(name = "changeEmploymentForm")
    @Excel(name = "转岗后用工形式",orderNum = "1", isImportField = "true" )
    private String changeEmploymentForm;

    @ExcelProperty("转岗后部门")
    @JSONField(name = "changeDept")
    @Excel(name = "转岗后部门",orderNum = "1", isImportField = "true" )
    private String changeDept;

    @ExcelProperty("转岗后岗位")
    @JSONField(name = "changeJob")
    @Excel(name = "转岗后岗位",orderNum = "1", isImportField = "true" )
    private String changeJob;

    private String approvalResult;

    private String requestId;

    private String fFlowId;

}
