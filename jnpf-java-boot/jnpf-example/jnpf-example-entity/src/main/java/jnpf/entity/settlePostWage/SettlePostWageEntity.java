package jnpf.entity.settlePostWage;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.util.Date;

/**
 * 漳州-定岗调薪表
 */
@FaModalName("定岗调薪表")
@Data
@TableName("ext_settle_post_wage")
public class SettlePostWageEntity extends SuperEntity<String> {

    @ExcelIgnore
    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("考核时间")
    private Date examineTime;

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("部门")
    private String dept;

    @ExcelProperty("工种")
    private String job;

    @ExcelProperty("进场时间")
    private Date approachTime;

    @ExcelProperty("来源单位")
    private String sourceUnit;

    /**
     * 1：定岗，2：调资
     */
    @ExcelProperty("调整类型")
    private Integer adjustType;

    @ExcelProperty("技能评分")
    private String skillRating;

    @ExcelProperty("劳动评分")
    private String laborRating;

    @ExcelProperty("工作评分")
    private String jobRating;

    @ExcelProperty("得分")
    private String score;

    @ExcelProperty("建议工资")
    private String suggestedWage;

}
