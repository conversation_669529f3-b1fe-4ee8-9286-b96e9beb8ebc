package jnpf.entity.specialWork;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.annotation.SqlEquals;
import jnpf.base.entity.SuperEntity;
import jnpf.entity.FileSave;
import jnpf.enums.specialWork.SpecialWorkFileInitialEnum;
import jnpf.enums.specialWork.SpecialWorkOperatorEnum;
import lombok.Data;

import java.util.Date;

/**
 * 特种作业证件
 */
@FaModalName("特种作业证件")
@Data
@TableName("ext_special_work_certificate")
public class SpecialWorkCertificateEntity extends SuperEntity<String> {

    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @SqlEquals
    @ExcelProperty("特种id")
    private String specialId;

    @SqlEquals
    @ExcelProperty("类型id")
    private String typeId;

    @ExcelProperty("类型")
    @TableField(exist = false)
    private String typeName;

    @SqlEquals
    @ExcelProperty("证件id")
    private String certificateId;

    @SqlEquals
    @ExcelProperty("审核图片ID")
    private String examinePicture;

    @SqlEquals
    @ExcelProperty("审核图片")
    private String signImg;

    @SqlEquals
    @ExcelProperty("审核图片上传")
    private Boolean isExaminePicture;

    // 证件
    @TableField(exist = false)
    private FileSave certificateFile;

    // 区分web/app
    @TableField(exist = false)
    private SpecialWorkOperatorEnum operatorState;

    @ExcelProperty("文件初始状态")
    private SpecialWorkFileInitialEnum fileInitial;

    @ExcelProperty("相关工种代号")
    private String jobCodename;

    @ExcelProperty("证件编号")
    private String certificateNum;

    @ExcelProperty("从事本工种工龄（年）")
    private String jobAge;

    @ExcelProperty("初次发证日期")
    private Date firstIssuingDate;

    @ExcelProperty("发证单位")
    private String issuingUnit;

    @ExcelProperty("有效期")
    private Date expirationDate;

    @ExcelProperty("复审日期")
    private Date reviewDate;

    @ExcelProperty("审核结果")
    private Boolean auditResults;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("今天日期")
    private Date today;

    @ExcelProperty("到期提醒")
    private Integer expirationReminder;

    @ExcelProperty("复审提醒")
    private Integer reviewReminder;

    @ExcelProperty("是否有监理证件")
    private Boolean isSupervisorCer;

    @ExcelProperty("编号")
    private String number;



}
