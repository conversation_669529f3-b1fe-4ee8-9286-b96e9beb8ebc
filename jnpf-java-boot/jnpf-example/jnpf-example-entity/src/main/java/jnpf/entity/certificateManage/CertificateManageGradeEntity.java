package jnpf.entity.certificateManage;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.annotation.SqlEquals;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.util.Date;

/**
 * 漳州-证件管理
 */
@FaModalName("证件管理-成绩")
@Data
@TableName("certificate_manage_grade")
public class CertificateManageGradeEntity extends SuperEntity<String> {

    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("培训类型ID")
    private String trainingId;

    @ExcelProperty("培训名称")
    private String trainingName;

    @ExcelProperty("培训时间")
    private Date trainingTime;

    @ExcelProperty("考试成绩")
    private String trainingGrade;

    @ExcelProperty("培训是否合格")
    private String trainingQualified;

    @TableField(exist = false)
    private String custodyNumber;

    @SqlEquals
    @ExcelProperty("用户ID")
    private String userId;

    @ExcelProperty("身份证号")
    private String idCard;

    @ExcelProperty("有效期结束")
    @TableField(exist = false)
    private Date validEnd;

    @TableField(exist = false)
    private String name;

    @TableField(exist = false)
    private String dept;

    @TableField(exist = false)
    private String team;

}
