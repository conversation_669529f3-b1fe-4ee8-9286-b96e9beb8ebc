package jnpf.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import jnpf.annotation.FaModalName;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 漳州-人员资料管理
 */
@FaModalName("人员资料管理")
@Data
@TableName("exit_factory_user")
public class ExitFactoryUserEntity extends SuperEntity<String> {

    @ExcelIgnore
    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("姓名")
    @SqlSearch
    private String name;

    @ExcelProperty("文件上传状态")
    private String fileUploadState;

    @ExcelProperty("问题原因")
    @SqlSearch
    private String problemCause;

    @ExcelProperty("性别")
    private String gender;

    @ExcelProperty("身份证")
    @SqlSearch
    private String idCard;

    @ExcelProperty("年龄")
    private String age;

    @ExcelProperty("部门")
    @SqlSearch
    private String dept;

    @ExcelProperty("岗位/工种")
    @SqlSearch
    private String job;

    @ExcelProperty("学历")
    @SqlSearch
    private String degree;

    @ExcelProperty("用工方式")
    @SqlSearch
    private String employmentMode;

    @ExcelProperty("劳动单位")
    @SqlSearch
    private String laborUnit;

    @ExcelProperty("联系方式")
    @SqlSearch
    private String phone;

    @ExcelProperty("持证情况")
    private String certificateStatus;

    @ExcelProperty("民族")
    @SqlSearch
    private String clans;



//    // 九大资料
//    // 保险文件
//    @ExcelIgnore
//    @TableField(value = "INSURANCE_FILE" , updateStrategy = FieldStrategy.DEFAULT)
//    private String insuranceFile;
//    // 合同
//    @ExcelIgnore
//    @TableField(value = "CONTRACT_FILE" , updateStrategy = FieldStrategy.DEFAULT)
//    private String contractFile;
//    // 合同签收表
//    @ExcelIgnore
//    @TableField(value = "CONTRACT_RECEIPTED_FILE" , updateStrategy = FieldStrategy.DEFAULT)
//    private String contractReceiptedFile;
//    // 进场承诺书
//    @ExcelIgnore
//    @TableField(value = "PROMISE_FILE" , updateStrategy = FieldStrategy.DEFAULT)
//    private String promiseFile;
//    // 进场登记表
//    @ExcelIgnore
//    @TableField(value = "REGISTRATION_FILE" , updateStrategy = FieldStrategy.DEFAULT)
//    private String registrationFile;
//    // 进场申请表
//    @ExcelIgnore
//    @TableField(value = "REQUEST_FILE" , updateStrategy = FieldStrategy.DEFAULT)
//    private String requestFile;
//    // 身份证文件
//    @ExcelIgnore
//    @TableField(value = "ID_CARD_FILE" , updateStrategy = FieldStrategy.DEFAULT)
//    private String idCardFile;
//    // 体检文件
//    @ExcelIgnore
//    @TableField(value = "PHYSICAL_EXAM_FILE" , updateStrategy = FieldStrategy.DEFAULT)
//    private String physicalExamFile;
//    // 知情反馈书
//    @ExcelIgnore
//    @TableField(value = "FEEDBACK_FILE" , updateStrategy = FieldStrategy.DEFAULT)
//    private String feedbackFile;

    // 退场承诺书
    @ExcelIgnore
    @TableField(updateStrategy = FieldStrategy.DEFAULT)
    private String exitCommitment;

    /**
     * 人员附件列表，从base_file_biz表关联查询得到
     */
    @ExcelIgnore
    @TableField(exist = false) // 非数据库字段
    private List<String> fileList;



}
