package jnpf.entity.specialWork;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.annotation.SqlEquals;
import jnpf.base.entity.SuperEntity;
import jnpf.enums.specialWork.SpecialWorkAuditStateEnum;
import jnpf.exception.DataException;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 特种作业
 */
@FaModalName("特种作业")
@Data
@TableName("ext_special_work")
public class SpecialWorkEntity extends SuperEntity<String> {

    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("编号")
    private String number;

    @ExcelProperty("用户ID")
    @TableField(exist = false)
    private String userId;

    @ExcelProperty("用户名称")
    private String userName;

    @ExcelProperty("用户部门ID")
    private String userDept;

    @ExcelProperty("用户部门")
    @TableField(exist = false)
    private String userDeptName;

    @ExcelProperty("用户身份证")
    private String userIdCard;

    @ExcelProperty("编审批状态")
    private SpecialWorkAuditStateEnum approvalState;

    @ExcelProperty("审核图片")
    private String examinePicture;

    @ExcelProperty("审核图片上传")
    private Boolean isExaminePicture;

    @SqlEquals
    @ExcelProperty("编制人员ID")
    private String compileUser;

    @SqlEquals
    @ExcelProperty("审核人员ID")
    private String examineUser;

    @SqlEquals
    @ExcelProperty("审批人员ID")
    private String approveUser;

    @ExcelProperty("性别")
    private Integer gender;

    @ExcelProperty("出生年月")
    private Date birthday;

    @ExcelProperty("年龄")
    private Integer age;

    @ExcelProperty("用户岗位ID")
    private String userPosition;

    @ExcelProperty("用户岗位名称")
    @TableField(exist = false)
    private String userPositionName;

    @ExcelProperty("学历ID")
    private String education;

    @ExcelProperty("学历名称")
    @TableField(exist = false)
    private String educationName;

    @ExcelProperty("职称")
    private String jobTitle;

    @ExcelProperty("作业类别/操作项目")
    private String jobType;

    @ExcelProperty("来源单位")
    private String sourceUnit;

    @ExcelProperty("进场日期")
    private Date entryDate;

    @ExcelProperty("退场日期")
    private Date exitDate;

    @ExcelProperty("监理报备日期")
    private Date supervisionReportDate;

    @ExcelProperty("是否有监理证件")
    private Boolean isSupervisorCer;

    /**
     * 获取审核的下一个状态
     * @return
     */
    public SpecialWorkAuditStateEnum getPassNextState() {
        switch (approvalState) {
            case DRAFT: return SpecialWorkAuditStateEnum.AUDIT;
            case AUDIT: return SpecialWorkAuditStateEnum.APPROVAL;
            case APPROVAL: return SpecialWorkAuditStateEnum.PASS;
            case PASS: return SpecialWorkAuditStateEnum.PASS;
        }
        throw new DataException("异常状态，请联系管理员");
    }

    /**
     * 获取退回的下一个状态
     * @return
     */
    public SpecialWorkAuditStateEnum getBackNextState() {
        switch (approvalState) {
            case DRAFT: return SpecialWorkAuditStateEnum.DRAFT;
            case AUDIT: return SpecialWorkAuditStateEnum.DRAFT;
            case APPROVAL: return SpecialWorkAuditStateEnum.AUDIT;
            case PASS: return SpecialWorkAuditStateEnum.APPROVAL;
        }
        throw new DataException("异常状态，请联系管理员");
    }

}
