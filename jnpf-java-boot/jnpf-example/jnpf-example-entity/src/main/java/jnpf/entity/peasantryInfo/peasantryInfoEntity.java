package jnpf.entity.peasantryInfo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.annotation.FaModalName;
import jnpf.base.entity.SuperEntity;
import lombok.Data;

import java.util.Date;

/**
 * 漳州-农民工信息表
 */
@FaModalName("农民工信息表")
@Data
@TableName("ext_approve_peasantry_info")
public class peasantryInfoEntity extends SuperEntity<String> {

    @ExcelIgnore
    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty("序号")
    private String id;

    @ExcelProperty("户口性质")
    private String natureAccount;

    @ExcelProperty("籍贯")
    private String nativePlace;

    @ExcelProperty("政治面貌")
    private String politicalOutlook;

    @ExcelProperty("毕业学校")
    private String graduationSchool;

    @ExcelProperty("所学专业")
    private String specialty;

    @ExcelProperty("毕业时间")
    private Date graduationTime;

    @ExcelProperty("办公地点")
    private String officeLocation;

    @ExcelProperty("办公电话")
    private String officePhone;

    @ExcelProperty("紧急联系人")
    private String emergencyContacts;

    @ExcelProperty("紧急联系人电话")
    private String emergencyContactsPhone;

    @ExcelProperty("婚姻状况")
    private String maritalStatus;

    @ExcelProperty("人数")
    private String number;

    @ExcelProperty("家庭住址")
    private String homeAddress;

    @TableField(exist = false)
    private String creatorName;

}
