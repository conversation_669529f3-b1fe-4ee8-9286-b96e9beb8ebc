# onlySelf 功能使用说明

## 功能描述

在数据列表获取接口中，新增了 `onlySelf` 参数，当该参数为 `true` 时，系统将只返回当前登录用户创建的数据记录。

## 使用方法

### 前端调用示例

```javascript
// 获取数据列表时，传入 onlySelf 参数
const requestData = {
  currentPage: 1,
  pageSize: 20,
  onlySelf: true  // 只看自己创建的数据
};

// 调用接口
fetch('/api/visualdev/OnlineDev/{modelId}/List', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(data => {
  console.log('只显示自己创建的数据:', data);
});
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| onlySelf | Boolean | 否 | 是否只显示自己创建的数据。true: 只显示自己创建的数据，false/null: 显示所有有权限的数据 |

## 实现原理

1. **有表数据过滤**: 在 SQL 查询中添加 `f_creator_user_id = 当前用户ID` 的条件
2. **无表数据过滤**: 在 MyBatis-Plus 查询中添加 `creatorUserId` 等于当前用户ID的条件
3. **子表数据过滤**: 对于有子表的情况，同样在子表查询中添加创建者过滤条件

## 影响范围

该功能影响以下接口和方法：

1. `POST /api/visualdev/OnlineDev/{modelId}/List` - 主要数据列表接口
2. `VisualDevListService.getDataList()` - 数据列表获取方法
3. `VisualDevListService.getListWithTable()` - 有表数据查询方法
4. `VisualDevListService.getWithoutTableData()` - 无表数据查询方法
5. `VisualDevListService.getRelationFormList()` - 关联表单列表方法

## 注意事项

1. 该功能基于数据表中的 `f_creator_user_id` 字段进行过滤
2. 只有当 `onlySelf` 参数明确设置为 `true` 时才会启用过滤
3. 该功能与现有的数据权限功能兼容，会在数据权限过滤的基础上进一步过滤
4. 对于子表数据，同样会应用创建者过滤逻辑

## 测试建议

1. 测试有表和无表两种情况下的数据过滤
2. 测试包含子表的复杂表单的数据过滤
3. 测试与现有数据权限功能的兼容性
4. 测试参数为 null、false、true 三种情况的行为
