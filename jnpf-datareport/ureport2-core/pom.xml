<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>ureport2-core</artifactId>
	<version>2.3.0-SNAPSHOT</version>
	<dependencies>
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-core-asl</artifactId>
			<version>1.9.11</version>
			<scope>compile</scope>
		</dependency>
	</dependencies>
	<parent>
		<artifactId>jnpf-datareport</artifactId>
		<groupId>com.jnpf</groupId>
		<version>1.0-SNAPSHOT</version>
	</parent>


	<licenses>
		<license>
			<name>The Apache License, Version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
		</license>
	</licenses>
	<developers>
		<developer>
			<name>Gaojie</name>
			<email>@bstek.com</email>
			<organization>Bstek</organization>
			<organizationUrl>http://www.bstek.com</organizationUrl>
		</developer>
	</developers>
	<scm>
		<connection>scm:git:https://github.com/youseries/ureport.git</connection>
		<developerConnection>scm:git:**************:youseries/ureport.git</developerConnection>
		<url>https://github.com/youseries/ureport</url>
    <tag>ureport2-core-2.2.4</tag>
  </scm>
	<organization>
		<name>Bstek</name>
		<url>http://www.bstek.com</url>
	</organization>	
	<name>UReport2 Core Project</name>
	<url>https://github.com/youseries/ureport/tree/master/ureport2-core</url>
</project>