<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>ureport2-console</artifactId>
	<version>2.3.0-SNAPSHOT</version>
	<parent>
		<groupId>com.jnpf</groupId>
		<artifactId>jnpf-datareport</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<dependencies>
		<dependency>
			<groupId>com.jnpf</groupId>
			<artifactId>ureport2-core</artifactId>
			<version>2.3.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.jnpf</groupId>
			<artifactId>ureport2-font</artifactId>
			<version>2.3.0-SNAPSHOT</version>
		</dependency>
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>8.16</version>
        </dependency>
    </dependencies>
	<licenses>
		<license>
			<name>The Apache License, Version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
		</license>
	</licenses>
	<developers>
		<developer>
			<name>Gaojie</name>
			<email>@bstek.com</email>
			<organization>Bstek</organization>
			<organizationUrl>http://www.bstek.com</organizationUrl>
		</developer>
	</developers>
	<scm>
		<connection>https://github.com/youseries/ureport.git</connection>
		<developerConnection>https://github.com/youseries/ureport.git</developerConnection>
		<url>https://github.com/youseries/ureport</url>
	</scm>
	<organization>
		<name>Bstek</name>
		<url>http://www.bstek.com</url>
	</organization>
	<name>UReport2 Console Project</name>
	<url>https://github.com/youseries/ureport/tree/master/ureport2-console</url>

	<build>
		<finalName>jnpf-datareport-${jnpf.version}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
				<configuration>
					<!-- 指定该Main Class为全局的唯一入口 -->
					<mainClass>com.bstek.ureport.console.DataReportApplication</mainClass>
					<layout>ZIP</layout>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
