package com.bstek.ureport.console.util;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.select.Select;
import org.springframework.jdbc.support.JdbcUtils;

import java.io.*;
import java.sql.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class JdbcUtil {

    /**
     * 连接Connection
     *
     * @param userName 用户名
     * @param password 密码
     * @param url      url
     * @return
     */
    public static Connection getConn(String userName, String password, String url) {
        final Connection[] conn = {null};
        Callable<String> task = new Callable<String>() {
            @Override
            public String call() throws Exception {
                //执行耗时代码
                try {
                    if (url.contains(DbType.MYSQL.getDb())) {
                        Class.forName("com.mysql.cj.jdbc.Driver");
                        String connectionUrl = url;
                        conn[0] = DriverManager.getConnection(connectionUrl, userName, password);
                    } else if (url.contains(DbType.ORACLE.getDb())) {
                        Class.forName("oracle.jdbc.OracleDriver");
                        String connectionUrl = url;
                        conn[0] = DriverManager.getConnection(connectionUrl, userName, password);
                    } else if (url.contains(DbType.SQL_SERVER.getDb())) {
                        Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                        String connectionUrl = url;
                        conn[0] = DriverManager.getConnection(connectionUrl, userName, password);
                    } else if (url.contains(DbType.DM.getDb())) {
                        Class.forName("dm.jdbc.driver.DmDriver");
                        String connectionUrl = url;
                        conn[0] = DriverManager.getConnection(connectionUrl, userName, password);
                    } else if (url.contains(DbType.POSTGRE_SQL.getDb())) {
                        Class.forName("org.postgresql.Driver");
                        String connectionUrl = url;
                        conn[0] = DriverManager.getConnection(connectionUrl, userName, password);
                    } else if (url.contains("kingbase")) {
                        Class.forName("com.kingbase8.Driver");
                        String connectionUrl = url;
                        conn[0] = DriverManager.getConnection(connectionUrl, userName, password);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
                return "jdbc连接成功";
            }
        };
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        Future<String> future = executorService.submit(task);
        try {
            //设置超时时间
            String rst = future.get(3L, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("连接数据库超时");
        } catch (Exception e) {
            log.error("获取数据异常254," + e.getStackTrace());
        } finally {
            executorService.shutdown();
        }

        return conn[0];
    }

    /**
     * 自定义sql语句(查)
     */
    public static ResultSet query(Connection conn, PreparedStatement preparedStatement, String sql) {
        ResultSet resultSet = null;
        try {
            //开启事务
            conn.setAutoCommit(false);
            preparedStatement = conn.prepareStatement(sql);
            resultSet = preparedStatement.executeQuery();
            //提交事务
            conn.commit();
            return resultSet;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    public static List<Map<String, String>> getDataTables(Connection conn, String tableSpace) throws SQLException {
        ResultSet rs = null;
        List<Map<String, String>> tables = new ArrayList<Map<String, String>>();
        try {
            String sql;
            String dbtype = conn.getMetaData().getDatabaseProductName().trim().toLowerCase();
            if (dbtype.contains("mysql")) {
                sql = "SELECT TABLE_NAME, " +
                        "CASE TABLE_TYPE WHEN 'BASE TABLE' THEN 'TABLE' ELSE 'VIEW' END AS TABLE_TYPE " +
                        "FROM INFORMATION_SCHEMA.TABLES WHERE table_schema = '" + conn.getCatalog() + "' ORDER BY TABLE_TYPE, TABLE_NAME ASC";
            } else if (dbtype.contains("oracle") || dbtype.contains("dm")) {
                //                rs = metaData.getTables("JNPF", schema, "%", new String[]{"TABLE", "VIEW"});
                sql = "SELECT TABLE_NAME, 'TABLE' AS TABLE_TYPE FROM user_tables";
            } else if(dbtype.contains("postgresql")){
                sql = "select tablename as \"TABLE_NAME\", 'TABLE' AS \"TABLE_TYPE\" from pg_tables where schemaname='public'";
            }else {
                sql = "SELECT TABLE_NAME," +
                        " CASE TABLE_TYPE WHEN 'BASE TABLE' THEN 'TABLE' ELSE 'VIEW' END AS TABLE_TYPE FROM INFORMATION_SCHEMA.TABLES" +
                        "  ORDER BY TABLE_TYPE,TABLE_NAME asc";
            }
            rs = query(conn, null, sql);
            while (rs.next()) {
                Map<String, String> table = new HashMap<String, String>();
                table.put("name", rs.getString("TABLE_NAME"));
                table.put("type", rs.getString("TABLE_TYPE"));
                tables.add(table);
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        } finally {
            JdbcUtils.closeResultSet(rs);
        }
        return tables;
    }

    private static final Pattern p = Pattern.compile("from[\\s]+([\\w\\.]+)");

    public static void checkSqlSafe(String sql, Connection conn, String tableSpace) throws JSQLParserException {
        sql = sql.toLowerCase();
        if (!(CCJSqlParserUtil.parse(sql) instanceof Select)) {
            throw new RuntimeException("只能使用查询语句");
        }
        List<Map<String, String>> tablesList = null;
        try {
            tablesList = getDataTables(conn, tableSpace);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
        Set<String> tables = tablesList.stream().map(k -> k.get("name").toLowerCase()).collect(Collectors.toSet());

        Matcher m = p.matcher(sql);
        while (m.find()) {
            if (!tables.contains(m.group(1).toLowerCase())) {
                throw new RuntimeException("非可查询表范围");
            }
        }
    }

}
