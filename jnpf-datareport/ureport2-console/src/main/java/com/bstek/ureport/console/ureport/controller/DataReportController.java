package com.bstek.ureport.console.ureport.controller;

import com.bstek.ureport.build.ReportBuilder;
import com.bstek.ureport.console.BaseServletAction;
import com.bstek.ureport.console.cache.TempObjectCache;
import com.bstek.ureport.console.config.DataSourceConfig;
import com.bstek.ureport.console.config.datasource.DataSourceContextHolder;
import com.bstek.ureport.console.designer.ReportDefinitionWrapper;
import com.bstek.ureport.console.ureport.entity.ReportEntity;
import com.bstek.ureport.console.ureport.entity.UserEntity;
import com.bstek.ureport.console.ureport.model.*;
import com.bstek.ureport.console.ureport.service.ReportService;
import com.bstek.ureport.console.ureport.service.UserService;
import com.bstek.ureport.console.ureport.util.*;
import com.bstek.ureport.console.util.*;
import com.bstek.ureport.definition.ReportDefinition;
import com.bstek.ureport.export.ReportRender;
import com.bstek.ureport.export.html.HtmlReport;
import com.bstek.ureport.model.Report;
import com.bstek.ureport.utils.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.SerializationConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.jdbc.support.JdbcUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.util.WebUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;
import java.io.*;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 核心控制层，大部分功能
 */
@Slf4j
public class DataReportController extends BaseServletAction {
    @Autowired
    private DataSourceConfig dataSourceConfig;
    @Autowired
    private DataSource dataSource;
    @Autowired
    private UpdateData updateData;

    @Autowired
    private ReportService reportService;
    @Autowired
    private ReportRender reportRender;
    @Autowired
    private UserService userService;

    @Override
    public void execute(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        String url = retriveMethod(req);
        String method = req.getMethod();
        if (method.equals("POST")) {
            if (url.equals("datareport/Data")) {
                savaData(req, resp);
            }
            if (url.startsWith("datareport/Data/Copy/")) {
                String id = url.substring(url.lastIndexOf("/")+1);
                copyReport(req, resp, id);
            }
            //导入
            if (url.contains("datareport/Data/Actions/Import")) {
                importDataReport(req, resp);
            }
        }
        if (method.equals("PUT")) {
            if (url.contains("Actions/State")) {
                //截取id
                String[] split = url.split("/");
                if (split.length == 5) {
                    String id = split[2];
                    stateDataReport(req, resp, id);
                }
            }else {
                //截取Data
                int strStartIndexs = url.indexOf("/");
                String ids = url.substring(strStartIndexs + 1);
                //截取id
                int strStartIndex = ids.indexOf("/");
                if (strStartIndex < 0) {
                    writeObjectToJson(resp, ActionResult.fail("数据不存在"));
                }
                String id = ids.substring(strStartIndex + 1);
                updateData(req, resp, id);
            }
        }
        if (method.equals("DELETE")) {
            //截取Data
            int strStartIndexs = url.indexOf("/");
            String ids = url.substring(strStartIndexs + 1);
            //截取id
            int strStartIndex = ids.indexOf("/");
            if (strStartIndex < 0) {
                writeObjectToJson(resp, ActionResult.fail("数据不存在"));
            } else {
                String id = ids.substring(strStartIndex + 1);
                deleteData(req, resp, id);
            }
        }
        if (method.equals("GET")) {
            if (url.equals("datareport/Data/init")) {
                init(req, resp);
            }
            if (url.equals("datareport/Data")) {
                getList(req, resp);
            }
            if (url.equals("datareport/Data/Selector")) {
                Selector(req, resp);
            }
            if (url.equals("datareport/Data/preview")) {
                previewData(req, resp);
            }
            if (url.contains("/Actions/Export")) {
                //截取id
                String[] split = url.split("/");
                if (split.length == 5) {
                    String id = split[2];
                    exportDataReport(req, resp, id);
                }
            }
            //打开报表
            if (!url.contains("/Actions/Export") && !url.equals("datareport/Data") && !url.equals("datareport/Data/init") && !url.equals("datareport/Data/Selector") && !url.contains("/preview")) {
                //截取Data
                int strStartIndex = url.indexOf("/");
                String ids = url.substring(strStartIndex + 1);
                //截取id
                int strStartIndexss = ids.indexOf("/");
                if (strStartIndexss < 0) {
                    writeObjectToJson(resp, ActionResult.fail("数据不存在"));
                }
                String id = ids.substring(strStartIndexss + 1);
                getInfo(req, resp, id);
            }
        }
    }

    /**
     * 导入
     *
     * @param req
     * @param resp
     */
    private void importDataReport(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        String fileContent = null;
        try {
            String contentType = req.getContentType();
            if (contentType != null && !"".equals(contentType) && contentType.contains("multipart/form-data")) {
                HttpSession session = req.getSession();
                MultipartResolver resolver = new CommonsMultipartResolver(session.getServletContext());
                MultipartHttpServletRequest multipartRequest = resolver.resolveMultipart(req);
                MultipartHttpServletRequest multipartRequest2 = WebUtils.getNativeRequest(multipartRequest, MultipartHttpServletRequest.class);
                MultipartFile file = multipartRequest2.getFile("file");
                if(file != null) {
                    fileContent = FileUtil.getFileContent(file);
                    session.setAttribute("fileContent", fileContent);
                }
            }
            ReportEntity entity = JSONUtil.getJsonToBean(fileContent, ReportEntity.class);
            if (entity.getContent() == null || "".equals(entity.getContent())){
                writeObjectToJson(resp, ActionResult.fail("导入失败，数据有误"));
            }
            if (entity != null && reportService.GetInfo(entity.getId()) == null && !reportService.IsExistByFullName(entity.getFullName(), entity.getId())) {
                String token = req.getHeader("Authorization");
                String userId = updateData.getUserId(token);
                entity.setCreatorTime(new Date());
                entity.setCreatorUser(userId);
                entity.setLastModifyTime(null);
                entity.setLastModifyUser(null);
                entity.setEnabledMark(0);
                reportService.Create(entity);
                writeObjectToJson(resp, ActionResult.success("导入成功"));
            } else {
                writeObjectToJson(resp, ActionResult.fail("数据已存在"));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            writeObjectToJson(resp, ActionResult.fail("导入失败，数据有误"));
        }
    }

    //初始化
    public void init(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        ReportDefinition reportDef = reportRender.parseReport("classpath:template/template.ureport.xml");
        writeObjectToJson(resp, ActionResult.success(new ReportDefinitionWrapper(reportDef)));
    }

    //列表
    public void getList(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        String currentPage = req.getParameter("currentPage");
        String pageSize = req.getParameter("pageSize");

        PaginationReport paginationReport = new PaginationReport();

        paginationReport.setKeyword(req.getParameter("keyword"));
        if(currentPage != null) {
            paginationReport.setCurrentPage(Long.parseLong(currentPage));
        }
        paginationReport.setCategory(req.getParameter("category"));
        if(pageSize != null) {
            paginationReport.setPageSize(Long.parseLong(pageSize));
        }

        List<ReportEntity> data = reportService.GetList(paginationReport);
        List<ReportListVO> list = JSONUtil.getJsonToList(data, ReportListVO.class);
        for (ReportListVO vo : list) {
            if (vo.getCreatorUser() != null && !vo.getCreatorUser().equals("")) {
                UserEntity entity = userService.getInfo(vo.getCreatorUser());
                if (entity != null) {
                    vo.setCreatorUser(entity.getRealName() + "/" + entity.getAccount());
                } else {
                    vo.setCreatorUser("");
                }
            }
            UserEntity entity1 = null;
            if (vo.getLastModifyUser() != null && !vo.getLastModifyUser().equals("")) {
                entity1 = userService.getInfo(vo.getLastModifyUser());
                if (entity1 != null) {
                    vo.setLastModifyUser(entity1.getRealName() + "/" + entity1.getAccount());
                } else {
                    vo.setLastModifyUser("");
                }
            }
        }
        PaginationVO pagination = JSONUtil.getJsonToBean(paginationReport, PaginationVO.class);
        writeObjectToJson(resp, ActionResult.page(list, pagination));
    }

    //下拉
    public void Selector(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        List<ReportEntity> data = reportService.GetList().stream().filter(t-> Objects.equals(t.getEnabledMark(),1)).collect(Collectors.toList());
        List<ReportSelectorVO> list = JSONUtil.getJsonToList(data, ReportSelectorVO.class);
        ListVO vo = new ListVO();
        vo.setList(list);
        writeObjectToJson(resp, ActionResult.success(vo));
    }

    //预览报表
    public void previewData(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        String id = req.getParameter("id");
        String page = req.getParameter("page");
        String token = req.getHeader("Authorization");
        String isSwitch = req.getParameter("isSwitch");
        if ("preview".equals(id)) {
            try {
                //未保存文件在编辑器预览
                ReportDefinition reportDefinition = (ReportDefinition) TempObjectCache.getObject(token);
                Map<String, Object> parameters = buildParameters(req);
                ReportBuilder reportBuilder = new ReportBuilder();
                Connection connection = null;
                if (!dataSourceConfig.isMultiTenancy() || dataSourceConfig.isMultiTenancyColumn()) {
                    connection = dataSource.getConnection();
                } else {
                    connection = JdbcUtil.getConn(dataSourceConfig.getUserName(), dataSourceConfig.getPassword(), dataSourceConfig.getUrl().replace("{dbName}", DataSourceContextHolder.getDatasourceName()));
                }
                Report report;
                try {
                    if("true".equals(isSwitch) && TempObjectCache.getObject(token + "_report")!=null){
                        report = (Report) TempObjectCache.getObject(token + "_report");
                    }else {
                        report = reportBuilder.buildReports(reportDefinition, parameters, connection);
                    }
                }finally {
                    JdbcUtils.closeConnection(connection);
                }
                UreportPreviewUtil previewUtil = new UreportPreviewUtil();
                HtmlReport htmlReport = null;
                //分页操作
                if ("".equals(page) || null == page || "0".equals(page)) {
                    htmlReport = previewUtil.loadReport(report, false, 0);
                } else {
                    htmlReport = previewUtil.loadReport(report, true, Integer.valueOf(page));
                    TempObjectCache.putObject(token + "_report", report);
                }
                htmlReport.setStyle(reportDefinition.getStyle());
                htmlReport.setSearchFormData(reportDefinition.buildSearchFormData(report.getContext().getDatasetMap(), parameters));
                writeObjectToJson(resp, ActionResult.success(htmlReport));
            }catch (QueryTimeoutException qt){
                log.error(qt.getMessage());
                writeObjectToJson(resp, ActionResult.fail("查询数据库超时， 请减少查询的数据量"));
            } catch (Exception e) {
//                e.printStackTrace();
                log.error(e.getMessage(), e);
                writeObjectToJson(resp, ActionResult.fail("缓存已超时"));
            }
        } else {
            //通过id预览
            ReportEntity entity = reportService.GetInfo(id);
            if (Objects.equals(entity.getEnabledMark(), 0)) {
                writeObjectToJson(resp, ActionResult.fail("报表已被禁用"));
            }
            Map<String, Object> parameters =buildParameters(req);
            UreportPreviewUtil previewUtil = new UreportPreviewUtil();
            Connection connection = null;
            if (!dataSourceConfig.isMultiTenancy() || dataSourceConfig.isMultiTenancyColumn()) {
                try {
                    connection = dataSource.getConnection();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
            } else {
                connection = JdbcUtil.getConn(dataSourceConfig.getUserName(), dataSourceConfig.getPassword(), dataSourceConfig.getUrl().replace("{dbName}", DataSourceContextHolder.getDatasourceName()));
            }
            ReportPreviewVO vo;
            if ("".equals(page) || null == page || "0".equals(page)) {
                vo = previewUtil.preview(entity, false, 1, parameters, connection, false);
            } else {
                vo = previewUtil.preview(entity, true, Integer.valueOf(page), parameters, connection, "true".equals(isSwitch));
            }
            vo.setEnabledMark(entity.getEnabledMark());
            try {
                connection.close();
            } catch (SQLException throwables) {
                log.error("点击预览报错:" + throwables.getMessage());
            }
            writeObjectToJson(resp, ActionResult.success(vo));
        }
    }

    //通过id打开到报表编辑器
    public void getInfo(HttpServletRequest req, HttpServletResponse resp, String id) throws ServletException, IOException {
        ReportEntity entity = reportService.GetInfo(id);
        ReportDefinition reportDefinition = null;
        if (entity == null) {
            writeObjectToJson(resp, ActionResult.fail("数据不存在"));
        }
        byte[] content = entity.getContent().getBytes("UTF-8");
        ByteArrayInputStream inputStream = new ByteArrayInputStream(content);
        reportDefinition = UreportUtil.parseReport(inputStream, entity.getFullName(), true);
        ReportDefinitionWrapper wrapper = new ReportDefinitionWrapper(reportDefinition);
        ReportInfoModel model = JSONUtil.getJsonToBean(entity, ReportInfoModel.class);
        writeObjectToJson(resp, ActionResult.successTOBase(wrapper, model));
    }

    //保存报表
    public void savaData(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        String payload = RequestUtil.getPayload(req);
        String token = req.getHeader("Authorization");
        Map<String, Object> map = JSONUtil.StringToMap(payload);
        if (map == null) {
            writeObjectToJson(resp, ActionResult.fail("不能添加空数据"));
        } else {
            ReportCrForm reportCrForm = JSONUtil.getJsonToBean(map, ReportCrForm.class);
            reportCrForm.setContent(UreportUtil.decodeContent(reportCrForm.getContent()));
            ReportEntity entity = JSONUtil.getJsonToBean(reportCrForm, ReportEntity.class);
            if (reportService.IsExistByFullName(entity.getFullName(), entity.getId())) {
                writeObjectToJson(resp, ActionResult.fail("名称不能重复"));
            } else {
                //检查表格内容是否合规
                byte[] content = entity.getContent().getBytes("UTF-8");
                ByteArrayInputStream inputStream = new ByteArrayInputStream(content);
                UreportUtil.parseReport(inputStream, entity.getFullName());

                String userId = updateData.getUserId(token);
                entity.setCreatorUser(userId);
                reportService.Create(entity);
                Object id = entity.getId();
                writeObjectToJson(resp, ActionResult.success(id));
            }
        }
    }

    //修改
    public void updateData(HttpServletRequest req, HttpServletResponse resp, String id) throws ServletException, IOException {
        String token = req.getHeader("Authorization");
        String payload = RequestUtil.getPayload(req);
        Map<String, Object> map = JSONUtil.StringToMap(payload);
        if (id == null || id.equals("")) {
            writeObjectToJson(resp, ActionResult.fail("数据不存在，修改失败"));
        } else {
            ReportUpForm reportUpForm = JSONUtil.getJsonToBean(map, ReportUpForm.class);
            reportUpForm.setContent(UreportUtil.decodeContent(reportUpForm.getContent()));
            ReportEntity entity = JSONUtil.getJsonToBean(reportUpForm, ReportEntity.class);
            //entity.setContent(UreportUtil.decodeContent(entity.getContent()));
            if (reportService.IsExistByFullName(entity.getFullName(), id)) {
                writeObjectToJson(resp, ActionResult.fail("名称不能重复"));
            } else {
                //检查表格内容是否合规
                byte[] content = entity.getContent().getBytes("UTF-8");
                ByteArrayInputStream inputStream = new ByteArrayInputStream(content);
                UreportUtil.parseReport(inputStream, entity.getFullName());

                String userId = updateData.getUserId(token);
                entity.setLastModifyUser(userId);
                boolean flags = reportService.Update(id, entity);
                if (flags) {
                    writeObjectToJson(resp, ActionResult.success("修改成功"));
                } else {
                    writeObjectToJson(resp, ActionResult.fail("数据不存在，修改失败"));
                }
            }
        }
    }

    //复制报表
    public void copyReport(HttpServletRequest req, HttpServletResponse resp, String id) throws ServletException, IOException {
        String token = req.getHeader("Authorization");
        if (id == null || id.equals("")) {
            writeObjectToJson(resp, ActionResult.fail("数据不存在，复制失败"));
        } else {
            ReportEntity entity = reportService.GetInfo(id);
            String userId = updateData.getUserId(token);
            entity.setCreatorUser(userId);
            boolean flags = reportService.Copy(entity);
            if (flags) {
                writeObjectToJson(resp, ActionResult.success("复制成功"));
            } else {
                writeObjectToJson(resp, ActionResult.fail("数据不存在，复制失败"));
            }
        }
    }
    //删除
    public void deleteData(HttpServletRequest req, HttpServletResponse resp, String id) throws ServletException, IOException {
        if (id == null || id.equals("")) {
            writeObjectToJson(resp, ActionResult.fail("数据不存在，修改失败"));
        } else {
            ReportEntity entity = reportService.GetInfo(id);
            boolean flags = reportService.Delete(entity);
            if (flags) {
                writeObjectToJson(resp, ActionResult.success("删除成功"));
            } else {
                writeObjectToJson(resp, ActionResult.fail("数据不存在，修改失败"));
            }
        }
    }

    /*//通过id导出报表
    public void exportData(HttpServletRequest req, HttpServletResponse resp, String id, String type) throws ServletException, IOException {
        ReportEntity entity = reportService.GetInfo(id);
        if (entity == null) {
            writeObjectToJson(resp, ActionResult.fail("导出数据不能为空"));
        }
        String fileName = entity.getFullName();
        resp.setCharacterEncoding("UTF-8");
        resp.setHeader("content-Type", "application/x-download");
        Connection connection = null;
        try {
            if (!dataSourceConfig.isMultiTenancy()) {
                connection = dataSource.getConnection();
            } else {
                connection = JdbcUtil.getConn(dataSourceConfig.getUserName(), dataSourceConfig.getPassword(), dataSourceConfig.getUrl().replace("{dbName}", DataSourceContextHolder.getDatasourceName()));
            }
        } catch (Exception throwables) {
            throwables.printStackTrace();
            log.error("数据源错误：" + throwables.getMessage());
        }
        if (type.toLowerCase().equals("pdf")) {
            resp.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".pdf", "UTF-8"));
            OutputStream outputStream = resp.getOutputStream();
            UreportPdfUtil pdfUtil = new UreportPdfUtil();
            pdfUtil.buildPdfToConnection(entity, outputStream, connection);
            outputStream.flush();
            outputStream.close();
        } else if (type.toLowerCase().equals("word")) {
            resp.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".docx", "UTF-8"));
            UreportWordUtil wordUtil = new UreportWordUtil();
            XWPFDocument xwpfDocument = wordUtil.buildWord(entity, connection);
            xwpfDocument.write(resp.getOutputStream());
            xwpfDocument.close();
        } else if (type.toLowerCase().equals("excel")) {
            resp.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));
            UreportExcelUtil excelUtil = new UreportExcelUtil();
            Workbook workbook = excelUtil.buildExcel(entity, false, false, connection);
            workbook.write(resp.getOutputStream());
            workbook.close();
        }
    }*/

    /**
     * 导出报表
     *
     * @param req
     * @param resp
     * @param id
     */
    private void exportDataReport(HttpServletRequest req, HttpServletResponse resp, String id) {
        ReportEntity entity = reportService.GetInfo(id);
        String objectToString = JSONUtil.getObjectToString(entity);
        DownUtil.downloadFile(objectToString, entity.getFullName() + ".json");
    }

    /**
     * 更新状态
     *
     * @param req
     * @param resp
     * @param id
     */
    private void stateDataReport(HttpServletRequest req, HttpServletResponse resp, String id) throws IOException {
        ReportEntity entity = reportService.GetInfo(id);
        if(entity==null){
            writeObjectToJson(resp, ActionResult.fail("更新接口状态失败，数据不存在"));
        }else {
            entity.setEnabledMark("0".equals(String.valueOf(entity.getEnabledMark()))?1:0);
            reportService.Update(id,entity);
            writeObjectToJson(resp, ActionResult.success("更新接口状态成功"));
        }
    }

    @Override
    public String url() {
        return "/api";
    }

    protected void writeObjectToJson(HttpServletResponse resp, Object obj) throws IOException {
        resp.setContentType("application/json");
        resp.setCharacterEncoding("UTF-8");
        ObjectMapper mapper = new ObjectMapper();
//        mapper.setSerializationInclusion(JsonSerialize.Inclusion.NON_NULL);
        mapper.configure(SerializationConfig.Feature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        OutputStream out = resp.getOutputStream();
        try {
            mapper.writeValue(out, obj);
        } finally {
            out.flush();
            out.close();
        }
    }

}
