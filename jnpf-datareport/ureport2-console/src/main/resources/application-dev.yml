# 配置端口
server:
  port: 30007

spring:
  datasource:
    # MySQL配置
    druid:
      dbinit: zz_szh_dev
      dbname: zz_szh_dev
      dbnull: zz_szh_dev
      url: jdbc:mysql://************:18735/{dbName}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true&serverTimezone=GMT%2B8
      username: zz_szh_dev
      password: zz_szh_dev@Dward#1264
      driver-class-name: com.mysql.cj.jdbc.Driver

  #Redis配置
  redis:
    database: 2
    host: ************
    port: 18901
    password: fa@redis#1276
    timeout: 3000
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数
        max-wait: -1ms  # 连接池最大阻塞等待时间（使用负值表示没有限制）
        min-idle: 0 # 连接池中的最小空闲连接
        max-idle: 8 # 连接池中的最大空闲连接
