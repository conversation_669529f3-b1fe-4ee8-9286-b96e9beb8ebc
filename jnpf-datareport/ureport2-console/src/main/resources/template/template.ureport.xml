<?xml version="1.0" encoding="UTF-8"?>
<ureport xmlns="http://www.example.org/ureport2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.example.org/ureport2 http://www.example.org/ureport2 ">
	<cell expand="None" name="A1" col="1" row="1">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="B1" col="2" row="1">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="C1" col="3" row="1">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="D1" col="4" row="1">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="E1" col="5" row="1">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="F1" col="6" row="1">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>

	<cell expand="None" name="A2" col="1" row="2">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="B2" col="2" row="2">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="C2" col="3" row="2">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="D2" col="4" row="2">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="E2" col="5" row="2">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="F2" col="6" row="2">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>

	<cell expand="None" name="A3" col="1" row="3">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="B3" col="2" row="3">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="C3" col="3" row="3">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="D3" col="4" row="3">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="E3" col="5" row="3">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="F3" col="6" row="3">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>

	<cell expand="None" name="A4" col="1" row="4">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="B4" col="2" row="4">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="C4" col="3" row="4">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="D4" col="4" row="4">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="E4" col="5" row="4">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="F4" col="6" row="4">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>

	<cell expand="None" name="A5" col="1" row="5">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="B5" col="2" row="5">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="C5" col="3" row="5">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="D5" col="4" row="5">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="E5" col="5" row="5">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="F5" col="6" row="5">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>

	<cell expand="None" name="A6" col="1" row="6">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="B6" col="2" row="6">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="C6" col="3" row="6">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="D6" col="4" row="6">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="E6" col="5" row="6">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>
	<cell expand="None" name="F6" col="6" row="6">
		<simple-value><![CDATA[]]></simple-value>
		<cell-style font-size="10" align="center" valign="middle"></cell-style>
	</cell>

	<row row-number="1" height="18"/>
	<row row-number="2" height="18"/>
	<row row-number="3" height="18"/>
	<row row-number="4" height="18"/>
	<row row-number="5" height="18"/>
	<row row-number="6" height="18"/>

	<column col-number="1" width="80"/>
	<column col-number="2" width="80"/>
	<column col-number="3" width="80"/>
	<column col-number="4" width="80"/>
	<column col-number="5" width="80"/>
	<column col-number="6" width="80"/>

	<paper type="A4" orientation="portrait" paging-mode="fitpage"></paper>

	<rapid windows="Ctrl+C" mac="command+C" fullName="复制"/>
	<rapid windows="Ctrl+V" mac="command+V" fullName="粘贴"/>
	<rapid windows="Ctrl+X" mac="command+X" fullName="剪切"/>
	<rapid windows="Ctrl+S" mac="command+S" fullName="保存"/>
	<rapid windows="Ctrl+Z" mac="command+Z" fullName="撤销"/>
	<rapid windows="Ctrl+Y" mac="command+Y" fullName="重做"/>
	<rapid windows="Ctrl+A" mac="command+A" fullName="全选"/>
	<rapid windows="Ctrl+F" mac="command+F" fullName="查找"/>
	<rapid windows="Ctrl+T" mac="command+T" fullName="设置"/>
	<rapid windows="Ctrl+P" mac="command+P" fullName="预览"/>
	<rapid windows="Ctrl+M" mac="command+M" fullName="合并单元格"/>
	<rapid windows="Ctrl+G" mac="command+G" fullName="取消合并单元格"/>
	<rapid windows="Ctrl+B" mac="command+B" fullName="加粗"/>
	<rapid windows="Ctrl+I" mac="command+I" fullName="倾斜"/>
	<rapid windows="Ctrl+U" mac="command+U" fullName="下划线"/>
	<rapid windows="Ctrl+1" mac="command+1" fullName="斜线表头"/>
	<rapid windows="Ctrl+H" mac="command+H" fullName="设置行高"/>
	<rapid windows="Ctrl+W" mac="command+W" fullName="设置列宽"/>
	<rapid windows="Del" mac="Del" fullName="清空内容"/>
	<rapid windows="Ctrl+N" mac="command+N" fullName="清空格式"/>
	<rapid windows="Ctrl+Del" mac="command+Del" fullName="清空所有"/>

	<rapid windows="Ctrl+Shift+C/Ctrl+Shift+V" mac="command+shift+C/command+shift+V" fullName="格式刷"/>
	<rapid windows="Ctrl+Alt+C" mac="Ctrl+Alt+C" fullName="复制单元格样式"/>
	<rapid windows="Ctrl+Alt+V" mac="Ctrl+Alt+C" fullName="粘贴单元格样式"/>
	<rapid windows="Alt+G" mac="Alt+G" fullName="切换到查询表单设计器"/>
	<rapid windows="Alt+E" mac="Alt+E" fullName="导入Excel模板文件"/>
	<rapid windows="Alt+B" mac="Alt+B" fullName="标题行"/>
	<rapid windows="Alt+T" mac="Alt+T" fullName="重复表头"/>
	<rapid windows="Alt+W" mac="Alt+W" fullName="重复表尾"/>
	<rapid windows="Alt+Z" mac="Alt+Z" fullName="总结行"/>
	<rapid windows="Alt+Q" mac="Alt+Q" fullName="取消行类型"/>
	<rapid windows="Alt+R" mac="Alt+R" fullName="删除行"/>
	<rapid windows="Alt+C" mac="Alt+C" fullName="删除列"/>
	<rapid windows="Alt+I" mac="Alt+I" fullName="插入图片"/>

	<rapid windows="↑/↓/←/→/Tab" mac="↑/↓/←/→/Tab" fullName="单元格移动"/>
	<rapid windows="Ctrl+↑/↓/←/→" mac="command+↑/↓/←/→" fullName="选中连续多列、多行"/>
	<rapid windows="Alt+↑" mac="lt+↑" fullName="插入行(上)"/>
	<rapid windows="Alt+↓" mac="Alt+↓b" fullName="插入行(下)"/>
	<rapid windows="Alt+←" mac="Alt+←" fullName="插入行(前)"/>
	<rapid windows="Alt+→" mac="Alt+→" fullName="插入行(后)"/>
</ureport>
