# 配置端口
server:
  port: 30007

spring:
  datasource:
    # MySQL配置
    druid:
      dbinit: zz_szh_pro
      dbname: zz_szh_pro
      dbnull: zz_szh_pro
      url: ****************************/{dbName}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true&serverTimezone=GMT%2B8
      username: zz_szh_pro
      password: zz_szh_pro@Dward#8371
      driver-class-name: com.mysql.cj.jdbc.Driver

  #Redis配置
  redis:
    database: 1
    host: localhost
    port: 18901
    password: fa@redis#1276
    timeout: 3000
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数
        max-wait: -1ms  # 连接池最大阻塞等待时间（使用负值表示没有限制）
        min-idle: 0 # 连接池中的最小空闲连接
        max-idle: 8 # 连接池中的最大空闲连接
